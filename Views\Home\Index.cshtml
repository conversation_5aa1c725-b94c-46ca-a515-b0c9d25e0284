﻿@model CarInsuranceWebsite.ViewModels.CompanySearchViewModel
@{
    ViewData["Title"] = "شركات التأمين في ليبيا";
}

<!-- Hero Section -->
<section class="hero-section bg-primary text-white py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="display-4 fw-bold mb-4">شركات تأمين السيارات في ليبيا</h1>
                <p class="lead mb-4">تصفح واختر من بين أفضل شركات التأمين ومكاتبها في ليبيا. مقارنة سهلة للخدمات والعروض المتاحة.</p>

                <!-- Search Form -->
                <form method="get" class="d-flex justify-content-center mb-4">
                    <div class="input-group" style="max-width: 500px;">
                        <input type="text" name="search" value="@Model.SearchTerm" class="form-control form-control-lg" placeholder="ابحث عن شركة تأمين...">
                        <button class="btn btn-light" type="submit">
                            <i class="fas fa-search"></i> بحث
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Companies Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="fw-bold">شركات التأمين المتاحة</h2>
                    <span class="text-muted">@Model.TotalCount شركة متاحة</span>
                </div>

                @if (Model.Companies.Any())
                {
                    <div class="row g-4">
                        @foreach (var company in Model.Companies)
                        {
                            <div class="col-lg-6 col-xl-4">
                                <div class="card h-100 shadow-sm border-0">
                                    <div class="card-body">
                                        <div class="d-flex align-items-start mb-3">
                                            @if (!string.IsNullOrEmpty(company.LogoUrl))
                                            {
                                                <img src="@company.LogoUrl" alt="@company.Name" class="me-3 rounded" style="width: 60px; height: 60px; object-fit: cover;">
                                            }
                                            else
                                            {
                                                <div class="bg-primary text-white rounded d-flex align-items-center justify-content-center me-3" style="width: 60px; height: 60px;">
                                                    <i class="fas fa-building fa-lg"></i>
                                                </div>
                                            }
                                            <div class="flex-grow-1">
                                                <h5 class="card-title mb-1">@company.Name</h5>
                                                <small class="text-muted">@company.Offices.Count() مكتب متاح</small>
                                            </div>
                                        </div>

                                        <p class="card-text text-muted">@company.Description</p>

                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="text-muted small">
                                                <i class="fas fa-phone me-1"></i>
                                                @company.PhoneNumber
                                            </div>
                                            <a href="@Url.Action("CompanyDetails", new { id = company.Id })" class="btn btn-primary btn-sm">
                                                عرض التفاصيل
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">لا توجد شركات تأمين</h4>
                        <p class="text-muted">@(string.IsNullOrEmpty(Model.SearchTerm) ? "لا توجد شركات تأمين متاحة حالياً" : "لم يتم العثور على نتائج للبحث المطلوب")</p>
                        @if (!string.IsNullOrEmpty(Model.SearchTerm))
                        {
                            <a href="@Url.Action("Index")" class="btn btn-primary">عرض جميع الشركات</a>
                        }
                    </div>
                }

                <!-- Pagination -->
                @if (Model.TotalPages > 1)
                {
                    <nav aria-label="صفحات النتائج" class="mt-4">
                        <ul class="pagination justify-content-center">
                            @if (Model.CurrentPage > 1)
                            {
                                <li class="page-item">
                                    <a class="page-link" href="@Url.Action("Index", new { page = Model.CurrentPage - 1, search = Model.SearchTerm })">السابق</a>
                                </li>
                            }

                            @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
                            {
                                <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                                    <a class="page-link" href="@Url.Action("Index", new { page = i, search = Model.SearchTerm })">@i</a>
                                </li>
                            }

                            @if (Model.CurrentPage < Model.TotalPages)
                            {
                                <li class="page-item">
                                    <a class="page-link" href="@Url.Action("Index", new { page = Model.CurrentPage + 1, search = Model.SearchTerm })">التالي</a>
                                </li>
                            }
                        </ul>
                    </nav>
                }
            </div>
        </div>
    </div>
</section>
