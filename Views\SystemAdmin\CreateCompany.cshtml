@using CarInsuranceWebsite.ViewModels
@model CreateCompanyViewModel
@{
    ViewData["Title"] = "إضافة شركة جديدة";
}

<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">إضافة شركة جديدة</h1>
        <a href="@Url.Action("Companies")" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>
            العودة للقائمة
        </a>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">بيانات الشركة</h6>
                </div>
                <div class="card-body">
                    <form asp-action="CreateCompany" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="Name" class="form-label"></label>
                                <input asp-for="Name" class="form-control" placeholder="اسم شركة التأمين" />
                                <span asp-validation-for="Name" class="text-danger"></span>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label asp-for="Email" class="form-label"></label>
                                <input asp-for="Email" class="form-control" placeholder="<EMAIL>" />
                                <span asp-validation-for="Email" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="PhoneNumber" class="form-label"></label>
                                <input asp-for="PhoneNumber" class="form-control" placeholder="+218213334455" />
                                <span asp-validation-for="PhoneNumber" class="text-danger"></span>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label asp-for="Website" class="form-label"></label>
                                <input asp-for="Website" class="form-control" placeholder="https://company.ly" />
                                <span asp-validation-for="Website" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Address" class="form-label"></label>
                            <input asp-for="Address" class="form-control" placeholder="العنوان الكامل للشركة" />
                            <span asp-validation-for="Address" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Description" class="form-label"></label>
                            <textarea asp-for="Description" class="form-control" rows="4" 
                                      placeholder="وصف مختصر عن الشركة وخدماتها"></textarea>
                            <span asp-validation-for="Description" class="text-danger"></span>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="@Url.Action("Companies")" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ الشركة
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
