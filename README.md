# موقع التأمين على السيارات - ليبيا 🇱🇾

موقع شامل لإدارة التأمين على السيارات في ليبيا مع دعم اللغة العربية ونظام إدارة متكامل.

## 🚀 تشغيل سريع مع GitHub Codespaces

### الطريقة الأسرع للحصول على رابط عام:

1. **ارفع هذا المشروع إلى GitHub**
2. **افتح GitHub Codespaces**
3. **شغل الأمر**: `bash start-codespaces.sh`
4. **احصل على الرابط العام من "Ports" tab**

### خطوات مفصلة:

#### 1. إنشاء Repository على GitHub
- اذهب إلى [github.com](https://github.com)
- اضغط "New repository"
- اسم المشروع: `car-insurance-libya`
- اجعله Public
- ارفع جميع ملفات هذا المشروع

#### 2. فتح Codespaces
- في صفحة المشروع على GitHub
- اضغط الزر الأخضر "Code"
- اختر "Codespaces"
- اضغط "Create codespace on main"

#### 3. تشغيل الموقع
```bash
# في terminal الخاص بـ Codespaces
bash start-codespaces.sh
```

#### 4. الحصول على الرابط العام
- اذهب إلى "Ports" tab في أسفل الشاشة
- ابحث عن Port 8080
- اضغط على أيقونة "Globe" لجعله عام
- انسخ الرابط وشاركه مع أصدقائك!

## 🌟 المميزات

- **واجهة عربية كاملة**: دعم RTL وتصميم متجاوب
- **نظام الأدوار**: مدير النظام ومديري الشركات
- **إدارة الشركات**: إضافة وتعديل شركات التأمين
- **إدارة المكاتب**: إدارة مكاتب الشركات والخدمات
- **البحث والتصفح**: للعملاء بدون تسجيل
- **الأمان**: نظام صلاحيات محكم
- **قاعدة بيانات**: SQLite للتطوير، PostgreSQL للإنتاج

## 🔐 بيانات تسجيل الدخول

### مدير النظام (السوبر أدمن)
- **البريد الإلكتروني**: `<EMAIL>`
- **كلمة المرور**: `Admin@123`

### مدير الشركة (للاختبار)
- **البريد الإلكتروني**: `<EMAIL>`
- **كلمة المرور**: `Manager@123`

## 🛠️ التقنيات المستخدمة

- **ASP.NET Core 9.0** - إطار العمل الرئيسي
- **Entity Framework Core** - ORM لقاعدة البيانات
- **SQLite** - قاعدة بيانات للتطوير والـ Codespaces
- **PostgreSQL** - قاعدة بيانات للإنتاج
- **SQL Server** - قاعدة بيانات للتطوير المحلي
- **Bootstrap 5 RTL** - تصميم متجاوب مع دعم العربية
- **ASP.NET Core Identity** - نظام المصادقة والصلاحيات

## 🌐 خيارات الرفع الأخرى

### Railway.app (للرفع الدائم)
1. ادفع الكود إلى GitHub
2. اربط المشروع مع Railway
3. أضف قاعدة بيانات PostgreSQL
4. سيتم الرفع تلقائياً

### Azure App Service
1. استخدم Azure CLI أو Portal
2. أنشئ App Service
3. اربطه مع GitHub
4. أضف SQL Database

## 📱 الاستخدام

الموقع مصمم ليكون:
- **متجاوب** - يعمل على الهاتف والكمبيوتر
- **سريع** - تحميل سريع وأداء ممتاز
- **آمن** - حماية البيانات ونظام صلاحيات
- **سهل الاستخدام** - واجهة بديهية باللغة العربية

## 🇱🇾 مخصص لليبيا

- **العملة**: الدينار الليبي (LYD)
- **الهاتف**: أرقام ليبية (+218)
- **الهوية**: رقم هوية ليبي (12 رقم)
- **المنطقة**: بنغازي، ليبيا
- **أيام العمل**: السبت - الخميس

## 👨‍💻 المطور

تم تطوير هذا المشروع بواسطة Augment Agent لخدمة سوق التأمين في ليبيا.

---

**ملاحظة**: هذا المشروع مجاني ومفتوح المصدر للاستخدام التعليمي والتجاري.
