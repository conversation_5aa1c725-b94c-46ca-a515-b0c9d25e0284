using CarInsuranceWebsite.Models;

namespace CarInsuranceWebsite.ViewModels
{
    public class CompanyManagerDashboardViewModel
    {
        public InsuranceCompany Company { get; set; } = null!;
        public int TotalOffices { get; set; }
        public int ActiveOffices { get; set; }
        public int TotalOffers { get; set; }
        public List<CompanyOffice> RecentOffices { get; set; } = new List<CompanyOffice>();
    }
}
