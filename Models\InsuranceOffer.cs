using System.ComponentModel.DataAnnotations;

namespace CarInsuranceWebsite.Models
{
    public enum InsuranceType
    {
        ThirdParty = 1,      // ضد الغير
        Comprehensive = 2,   // شامل
        Enhanced = 3         // محسن
    }

    public class InsuranceOffer
    {
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string Title { get; set; } = string.Empty;

        [StringLength(1000)]
        public string Description { get; set; } = string.Empty;

        [Required]
        public InsuranceType Type { get; set; }

        [Required]
        public decimal MinPrice { get; set; }

        [Required]
        public decimal MaxPrice { get; set; }

        [StringLength(500)]
        public string Features { get; set; } = string.Empty; // JSON string for features list

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? UpdatedAt { get; set; }

        // Foreign key
        public int CompanyOfficeId { get; set; }

        // Navigation properties
        public virtual CompanyOffice CompanyOffice { get; set; } = null!;
    }
}
