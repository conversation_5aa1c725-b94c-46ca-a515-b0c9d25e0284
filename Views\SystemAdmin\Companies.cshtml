@using CarInsuranceWebsite.Models
@model IEnumerable<InsuranceCompany>
@{
    ViewData["Title"] = "إدارة الشركات";
}

<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">إدارة الشركات</h1>
        <a href="@Url.Action("CreateCompany")" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>
            إضافة شركة جديدة
        </a>
    </div>

    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            @TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <!-- DataTales Example -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">قائمة الشركات</h6>
        </div>
        <div class="card-body">
            @if (Model.Any())
            {
                <div class="table-responsive">
                    <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>اسم الشركة</th>
                                <th>البريد الإلكتروني</th>
                                <th>رقم الهاتف</th>
                                <th>عدد المكاتب</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var company in Model)
                            {
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                <div class="icon-circle @(company.IsActive ? "bg-success" : "bg-secondary")">
                                                    <i class="fas fa-building text-white"></i>
                                                </div>
                                            </div>
                                            <div>
                                                <div class="font-weight-bold">@company.Name</div>
                                                <div class="small text-gray-500">@company.Description</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>@company.Email</td>
                                    <td>@company.PhoneNumber</td>
                                    <td>
                                        <span class="badge badge-info">@company.Offices.Count</span>
                                    </td>
                                    <td>
                                        @if (company.IsActive)
                                        {
                                            <span class="badge badge-success">نشط</span>
                                        }
                                        else
                                        {
                                            <span class="badge badge-secondary">غير نشط</span>
                                        }
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="@Url.Action("EditCompany", new { id = company.Id })" 
                                               class="btn btn-sm btn-outline-primary" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="confirmDelete(@company.Id, '@company.Name')" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <i class="fas fa-building fa-3x text-gray-300 mb-3"></i>
                    <h5 class="text-gray-500">لا توجد شركات مسجلة</h5>
                    <p class="text-gray-400">ابدأ بإضافة شركة تأمين جديدة</p>
                    <a href="@Url.Action("CreateCompany")" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة شركة جديدة
                    </a>
                </div>
            }
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الشركة <strong id="companyName"></strong>؟</p>
                <p class="text-danger small">تحذير: سيتم حذف جميع المكاتب والعروض المرتبطة بهذه الشركة.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.icon-circle {
    height: 2.5rem;
    width: 2.5rem;
    border-radius: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.badge {
    font-size: 0.75em;
}

.badge-success {
    background-color: #1cc88a;
}

.badge-secondary {
    background-color: #858796;
}

.badge-info {
    background-color: #36b9cc;
}
</style>

<script>
function confirmDelete(companyId, companyName) {
    document.getElementById('companyName').textContent = companyName;
    document.getElementById('deleteForm').action = '@Url.Action("DeleteCompany")/' + companyId;
    
    var modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}
</script>
