{"name": "Car Insurance Libya", "image": "mcr.microsoft.com/devcontainers/dotnet:9.0", "features": {"ghcr.io/devcontainers/features/git:1": {}, "ghcr.io/devcontainers/features/github-cli:1": {}}, "forwardPorts": [5000, 5001, 8080], "portsAttributes": {"5000": {"label": "HTTP", "onAutoForward": "notify"}, "5001": {"label": "HTTPS", "onAutoForward": "notify"}, "8080": {"label": "Web App", "onAutoForward": "openBrowser"}}, "postCreateCommand": "dotnet restore && dotnet build", "customizations": {"vscode": {"extensions": ["ms-dotnettools.csharp", "ms-dotnettools.vscode-dotnet-runtime"]}}}