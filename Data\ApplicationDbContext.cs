using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using CarInsuranceWebsite.Models;

namespace CarInsuranceWebsite.Data
{
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        public DbSet<InsuranceCompany> InsuranceCompanies { get; set; }
        public DbSet<CompanyOffice> CompanyOffices { get; set; }
        public DbSet<InsuranceOffer> InsuranceOffers { get; set; }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            // Configure InsuranceCompany entity
            builder.Entity<InsuranceCompany>(entity =>
            {
                entity.HasIndex(e => e.Name).IsUnique();
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
            });

            // Configure CompanyOffice entity
            builder.Entity<CompanyOffice>(entity =>
            {
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");

                entity.HasOne(d => d.InsuranceCompany)
                    .WithMany(p => p.Offices)
                    .HasForeignKey(d => d.InsuranceCompanyId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Configure InsuranceOffer entity
            builder.Entity<InsuranceOffer>(entity =>
            {
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");

                entity.HasOne(d => d.CompanyOffice)
                    .WithMany(p => p.Offers)
                    .HasForeignKey(d => d.CompanyOfficeId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Configure ApplicationUser relationship with InsuranceCompany
            builder.Entity<ApplicationUser>(entity =>
            {
                entity.HasOne(e => e.InsuranceCompany)
                    .WithMany(e => e.Managers)
                    .HasForeignKey(e => e.InsuranceCompanyId)
                    .OnDelete(DeleteBehavior.SetNull);
            });
        }
    }
}
