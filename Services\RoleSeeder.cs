using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using CarInsuranceWebsite.Models;
using CarInsuranceWebsite.Data;

namespace CarInsuranceWebsite.Services
{
    public static class RoleSeeder
    {
        public static async Task SeedRolesAndAdminAsync(IServiceProvider serviceProvider)
        {
            var roleManager = serviceProvider.GetRequiredService<RoleManager<IdentityRole>>();
            var userManager = serviceProvider.GetRequiredService<UserManager<ApplicationUser>>();
            var context = serviceProvider.GetRequiredService<ApplicationDbContext>();

            // Create roles if they don't exist
            string[] roleNames = { "SystemAdmin", "CompanyManager" };

            foreach (var roleName in roleNames)
            {
                var roleExist = await roleManager.RoleExistsAsync(roleName);
                if (!roleExist)
                {
                    await roleManager.CreateAsync(new IdentityRole(roleName));
                }
            }

            // Create SystemAdmin user if it doesn't exist
            var systemAdminEmail = "<EMAIL>";
            var systemAdminUser = await userManager.FindByEmailAsync(systemAdminEmail);

            if (systemAdminUser == null)
            {
                var adminUser = new ApplicationUser
                {
                    UserName = systemAdminEmail,
                    Email = systemAdminEmail,
                    FirstName = "مدير",
                    LastName = "النظام",
                    NationalId = "123456789012",
                    Address = "بنغازي، ليبيا",
                    DateOfBirth = new DateTime(1980, 1, 1),
                    PhoneNumber = "0612345678",
                    CreatedAt = DateTime.UtcNow,
                    IsActive = true,
                    EmailConfirmed = true
                };

                var result = await userManager.CreateAsync(adminUser, "Admin@123");

                if (result.Succeeded)
                {
                    await userManager.AddToRoleAsync(adminUser, "SystemAdmin");
                }
            }

            // Create sample insurance company
            var sampleCompany = await context.InsuranceCompanies.FirstOrDefaultAsync(c => c.Name == "شركة ليبيا للتأمين");
            if (sampleCompany == null)
            {
                sampleCompany = new InsuranceCompany
                {
                    Name = "شركة ليبيا للتأمين",
                    Description = "شركة رائدة في مجال التأمين في ليبيا تقدم خدمات تأمين شاملة للسيارات",
                    PhoneNumber = "+218213334455",
                    Email = "<EMAIL>",
                    Address = "شارع الجمهورية، بنغازي، ليبيا",
                    Website = "https://libya-insurance.ly",
                    IsActive = true
                };
                context.InsuranceCompanies.Add(sampleCompany);
                await context.SaveChangesAsync();
            }

            // Create sample company manager user
            var managerEmail = "<EMAIL>";
            var managerUser = await userManager.FindByEmailAsync(managerEmail);

            if (managerUser == null)
            {
                managerUser = new ApplicationUser
                {
                    UserName = managerEmail,
                    Email = managerEmail,
                    FirstName = "أحمد",
                    LastName = "الليبي",
                    NationalId = "987654321098",
                    Address = "بنغازي، ليبيا",
                    DateOfBirth = new DateTime(1985, 5, 15),
                    PhoneNumber = "+218913456789",
                    CreatedAt = DateTime.UtcNow,
                    IsActive = true,
                    EmailConfirmed = true,
                    InsuranceCompanyId = sampleCompany.Id
                };

                var result = await userManager.CreateAsync(managerUser, "Manager@123");
                if (result.Succeeded)
                {
                    await userManager.AddToRoleAsync(managerUser, "CompanyManager");
                }
            }
        }
    }
}
