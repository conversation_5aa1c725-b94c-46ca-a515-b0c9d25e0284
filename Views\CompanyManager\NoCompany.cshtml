@{
    ViewData["Title"] = "لم يتم تعيين شركة";
}

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-sm border-0">
                <div class="card-body text-center p-5">
                    <div class="mb-4">
                        <i class="fas fa-building fa-5x text-muted"></i>
                    </div>
                    
                    <h2 class="h4 mb-3">لم يتم تعيينك لشركة تأمين</h2>
                    
                    <p class="text-muted mb-4">
                        @ViewBag.Message
                    </p>
                    
                    <div class="alert alert-info" role="alert">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> بعد تعيينك لشركة تأمين من قبل مدير النظام، ستتمكن من الوصول إلى لوحة تحكم الشركة وإدارة المكاتب والعروض.
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                        <a asp-controller="Home" asp-action="Index" class="btn btn-primary">
                            <i class="fas fa-home me-2"></i>العودة للصفحة الرئيسية
                        </a>
                        <a asp-controller="Account" asp-action="Logout" class="btn btn-outline-secondary">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
