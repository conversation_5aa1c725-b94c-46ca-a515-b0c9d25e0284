@model CarInsuranceWebsite.ViewModels.CompanyManagerDashboardViewModel
@{
    ViewData["Title"] = "لوحة تحكم الشركة";
}

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">لوحة تحكم الشركة</h1>
                <div class="text-muted">
                    <i class="fas fa-building me-2"></i>@Model.Company.Name
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي المكاتب
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.TotalOffices</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-building fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                المكاتب النشطة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.ActiveOffices</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                إجمالي العروض
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@Model.TotalOffers</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clipboard-list fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                معدل النشاط
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                @(Model.TotalOffices > 0 ? Math.Round((double)Model.ActiveOffices / Model.TotalOffices * 100, 1) : 0)%
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-percentage fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Company Information -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">معلومات الشركة</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12 mb-3">
                            <strong>اسم الشركة:</strong> @Model.Company.Name
                        </div>
                        <div class="col-12 mb-3">
                            <strong>الوصف:</strong> @Model.Company.Description
                        </div>
                        <div class="col-12 mb-3">
                            <strong>رقم الهاتف:</strong> @Model.Company.PhoneNumber
                        </div>
                        <div class="col-12 mb-3">
                            <strong>البريد الإلكتروني:</strong> @Model.Company.Email
                        </div>
                        <div class="col-12 mb-3">
                            <strong>العنوان:</strong> @Model.Company.Address
                        </div>
                        @if (!string.IsNullOrEmpty(Model.Company.Website))
                        {
                            <div class="col-12 mb-3">
                                <strong>الموقع الإلكتروني:</strong> 
                                <a href="@Model.Company.Website" target="_blank">@Model.Company.Website</a>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Offices -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">المكاتب الحديثة</h6>
                    <a asp-action="Offices" class="btn btn-sm btn-primary">
                        <i class="fas fa-eye me-1"></i>عرض الكل
                    </a>
                </div>
                <div class="card-body">
                    @if (Model.RecentOffices.Any())
                    {
                        @foreach (var office in Model.RecentOffices)
                        {
                            <div class="d-flex align-items-center py-2 border-bottom">
                                <div class="flex-grow-1">
                                    <div class="fw-bold">@office.Name</div>
                                    <small class="text-muted">@office.Address</small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-@(office.IsActive ? "success" : "secondary")">
                                        @(office.IsActive ? "نشط" : "غير نشط")
                                    </span>
                                </div>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-building fa-3x mb-3"></i>
                            <p>لا توجد مكاتب بعد</p>
                            <a asp-action="CreateOffice" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>إضافة مكتب جديد
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">الإجراءات السريعة</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a asp-action="CreateOffice" class="btn btn-outline-primary w-100">
                                <i class="fas fa-plus-circle fa-2x d-block mb-2"></i>
                                إضافة مكتب جديد
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a asp-action="Offices" class="btn btn-outline-info w-100">
                                <i class="fas fa-building fa-2x d-block mb-2"></i>
                                إدارة المكاتب
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a asp-action="Offers" class="btn btn-outline-success w-100">
                                <i class="fas fa-clipboard-list fa-2x d-block mb-2"></i>
                                إدارة العروض
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="#" class="btn btn-outline-warning w-100">
                                <i class="fas fa-chart-bar fa-2x d-block mb-2"></i>
                                التقارير
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
</style>
