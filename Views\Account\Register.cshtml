@model CarInsuranceWebsite.ViewModels.RegisterViewModel
@{
    ViewData["Title"] = "إنشاء حساب جديد";
}

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow-sm border-0">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <h2 class="h3 mb-3">إنشاء حساب جديد</h2>
                        <p class="text-muted">انضم إلينا كمدير شركة تأمين</p>
                    </div>

                    <form asp-action="Register" method="post">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="FirstName" class="form-label"></label>
                                <input asp-for="FirstName" class="form-control form-control-lg" placeholder="أدخل الاسم الأول" />
                                <span asp-validation-for="FirstName" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="LastName" class="form-label"></label>
                                <input asp-for="LastName" class="form-control form-control-lg" placeholder="أدخل الاسم الأخير" />
                                <span asp-validation-for="LastName" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Email" class="form-label"></label>
                            <input asp-for="Email" class="form-control form-control-lg" placeholder="أدخل البريد الإلكتروني" />
                            <span asp-validation-for="Email" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="PhoneNumber" class="form-label"></label>
                            <input asp-for="PhoneNumber" class="form-control form-control-lg" placeholder="أدخل رقم الهاتف" />
                            <span asp-validation-for="PhoneNumber" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Password" class="form-label"></label>
                            <input asp-for="Password" class="form-control form-control-lg" placeholder="أدخل كلمة المرور" />
                            <span asp-validation-for="Password" class="text-danger"></span>
                        </div>

                        <div class="mb-4">
                            <label asp-for="ConfirmPassword" class="form-label"></label>
                            <input asp-for="ConfirmPassword" class="form-control form-control-lg" placeholder="أعد إدخال كلمة المرور" />
                            <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-user-plus me-2"></i>إنشاء الحساب
                            </button>
                        </div>
                    </form>

                    <hr class="my-4">

                    <div class="text-center">
                        <p class="mb-0">لديك حساب بالفعل؟ 
                            <a asp-action="Login" class="text-decoration-none">تسجيل الدخول</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
