using Microsoft.AspNetCore.Identity;
using System.ComponentModel.DataAnnotations;

namespace CarInsuranceWebsite.Models
{
    public class ApplicationUser : IdentityUser
    {
        [Required]
        [StringLength(100)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string LastName { get; set; } = string.Empty;

        [StringLength(20)]
        public string? NationalId { get; set; }

        [StringLength(200)]
        public string? Address { get; set; }

        public DateTime DateOfBirth { get; set; }

        // For company managers only
        public int? InsuranceCompanyId { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? UpdatedAt { get; set; }

        public bool IsActive { get; set; } = true;

        // Navigation properties
        public virtual InsuranceCompany? InsuranceCompany { get; set; }
    }

    public enum UserRole
    {
        SystemAdmin,      // مدير النظام
        CompanyManager    // مدير الشركة
    }
}
