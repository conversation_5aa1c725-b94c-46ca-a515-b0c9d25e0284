using System.ComponentModel.DataAnnotations;

namespace CarInsuranceWebsite.ViewModels
{
    public class EditOfficeViewModel
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم المكتب مطلوب")]
        [StringLength(100, ErrorMessage = "اسم المكتب يجب أن يكون أقل من 100 حرف")]
        [Display(Name = "اسم المكتب")]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "العنوان مطلوب")]
        [StringLength(200, ErrorMessage = "العنوان يجب أن يكون أقل من 200 حرف")]
        [Display(Name = "العنوان")]
        public string Address { get; set; } = string.Empty;

        [Required(ErrorMessage = "رقم الهاتف مطلوب")]
        [Phone(ErrorMessage = "رقم الهاتف غير صحيح")]
        [Display(Name = "رقم الهاتف")]
        public string PhoneNumber { get; set; } = string.Empty;

        [Display(Name = "الوصف")]
        public string Description { get; set; } = string.Empty;

        [Display(Name = "ساعات العمل")]
        public string WorkingHours { get; set; } = string.Empty;

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;
    }
}
